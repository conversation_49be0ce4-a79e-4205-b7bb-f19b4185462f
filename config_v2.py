import os
from dotenv import load_dotenv
from agents import AsyncOpenAI, OpenAIChatCompletionsModel
from agents.run import RunConfig

# Load environment variables with explicit path
load_dotenv(".env")

# Get API key with explicit fallback
gemini_api_key = os.getenv("GEMINI_API_KEY")
if not gemini_api_key:
    raise ValueError("GEMINI_API_KEY not found in environment variables")

print(f"Config v2 - API Key loaded: {gemini_api_key[:10]}...{gemini_api_key[-5:]}")

# Try alternative configuration
try:
    # Method 1: Standard configuration
    external_client = AsyncOpenAI(
        api_key=gemini_api_key,
        base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
    )
    
    model = OpenAIChatCompletionsModel(
        model="gemini-2.0-flash",
        openai_client=external_client
    )
    
    config = RunConfig(
        model=model,
        model_provider=external_client,
        tracing_disabled=True
    )
    
    print("Config v2 - Standard configuration created successfully")
    
except Exception as e:
    print(f"Config v2 - Error in standard configuration: {e}")
    
    # Method 2: Alternative configuration
    try:
        external_client = AsyncOpenAI(
            api_key=gemini_api_key,
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
            timeout=30.0,
            max_retries=3
        )
        
        model = OpenAIChatCompletionsModel(
            model="gemini-2.0-flash",
            openai_client=external_client
        )
        
        config = RunConfig(
            model=model,
            model_provider=external_client,
            tracing_disabled=True
        )
        
        print("Config v2 - Alternative configuration created successfully")
        
    except Exception as e2:
        print(f"Config v2 - Error in alternative configuration: {e2}")
        raise
