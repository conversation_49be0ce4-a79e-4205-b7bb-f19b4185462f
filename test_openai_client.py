import os
from dotenv import load_dotenv, find_dotenv
from agents import AsyncOpenAI, OpenAIChatCompletionsModel
import asyncio

# Load environment variables
load_dotenv(find_dotenv())
gemini_api_key = os.getenv("GEMINI_API_KEY")

print("=== Testing OpenAI Client with Gemini ===")
print(f"API Key: {gemini_api_key[:10]}...{gemini_api_key[-5:]}")

# Test the exact same setup as your config
external_client = AsyncOpenAI(
    api_key=gemini_api_key,
    base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
)

model = OpenAIChatCompletionsModel(
    model="gemini-2.0-flash",
    openai_client=external_client
)

async def test_model():
    try:
        print("Testing model with simple message...")
        
        # Test with a simple message
        messages = [{"role": "user", "content": "Hello, say hi back"}]
        
        # Try to get a response
        response = await external_client.chat.completions.create(
            model="gemini-2.0-flash",
            messages=messages,
            stream=False
        )
        
        print("✅ Success! Model responded:")
        print(response.choices[0].message.content)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"Error type: {type(e)}")

# Run the test
if __name__ == "__main__":
    asyncio.run(test_model())
