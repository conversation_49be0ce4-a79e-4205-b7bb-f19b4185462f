from agents import Runner, Agent
import chainlit as cl
from openai.types.responses import ResponseTextDeltaEvent
from config import config, model

agent = Agent(
    name="Agent Learner",
    instructions="Greeting from <PERSON> Learner",
    model=model
)

@cl.on_chat_start
async def handle_start_chat():
    cl.user_session.set("history" ,[])
    await cl.Message(content="<PERSON><PERSON><PERSON><PERSON> O Alikum from Agent Learner").send()

@cl.on_message
async def handle_message(message: cl.Message):
    history = cl.user_session.get("history")

    msg = cl.Message(content="")
    await msg.send()

    history.append({"role": "user", "content": message.content})

    # Debug: Print what we're sending
    print(f"Debug - Sending to agent: {history}")

    try:
        result = Runner.run_streamed(
            agent,
            input=history,
            run_config=config
        )
        async for event in result.stream_events():
            if event.type == "raw_response_event" and isinstance(event.data, ResponseTextDeltaEvent):
                await msg.stream_token(event.data.delta)
        history.append({"role": "assistant", "content": result.final_output})
        cl.user_session.set("history", history)
        print(f"Debug - Response received: {result.final_output}")

    except Exception as e:
        print(f"Error in handle_message: {e}")
        await cl.Message(content=f"Sorry, I encountered an error: {str(e)}").send()