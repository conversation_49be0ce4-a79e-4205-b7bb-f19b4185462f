import os
from dotenv import load_dotenv, find_dotenv
from agents import AsyncOpenAI, OpenAIChatCompletionsModel, Agent, Runner
from agents.run import RunConfig

# Load environment variables
load_dotenv(find_dotenv())
gemini_api_key = os.getenv("GEMINI_API_KEY")

print("=== Debug Config Setup ===")
print(f"API Key: {gemini_api_key[:10]}...{gemini_api_key[-5:]}")

# Test different base URL formats
base_urls = [
    "https://generativelanguage.googleapis.com/v1beta/openai/",
    "https://generativelanguage.googleapis.com/v1beta/openai",
    f"https://generativelanguage.googleapis.com/v1beta/openai/?key={gemini_api_key}",
]

for i, base_url in enumerate(base_urls):
    print(f"\n--- Testing Base URL {i+1}: {base_url} ---")
    
    try:
        # Setup client 
        external_client = AsyncOpenAI(
            api_key=gemini_api_key,
            base_url=base_url,
        )
        
        # Test simple completion
        import asyncio
        
        async def test_completion():
            try:
                response = await external_client.chat.completions.create(
                    model="gemini-2.0-flash",
                    messages=[{"role": "user", "content": "Say hello"}],
                    stream=False
                )
                print(f"✅ Success with base_url {i+1}")
                return True
            except Exception as e:
                print(f"❌ Failed with base_url {i+1}: {e}")
                return False
        
        success = asyncio.run(test_completion())
        
        if success:
            print(f"✅ Found working base URL: {base_url}")
            break
            
    except Exception as e:
        print(f"❌ Setup failed for base_url {i+1}: {e}")

# Test alternative: Direct API key in URL
print("\n=== Testing Alternative: API Key in URL ===")
try:
    # Some APIs require the key in the URL instead of headers
    alt_client = AsyncOpenAI(
        api_key="dummy",  # Some APIs ignore this when key is in URL
        base_url=f"https://generativelanguage.googleapis.com/v1beta/openai/?key={gemini_api_key}",
    )
    
    async def test_alt():
        try:
            response = await alt_client.chat.completions.create(
                model="gemini-2.0-flash",
                messages=[{"role": "user", "content": "Say hello"}],
                stream=False
            )
            print("✅ Alternative method works!")
            return True
        except Exception as e:
            print(f"❌ Alternative method failed: {e}")
            return False
    
    asyncio.run(test_alt())
    
except Exception as e:
    print(f"❌ Alternative setup failed: {e}")
