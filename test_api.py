import os
from dotenv import load_dotenv, find_dotenv

# Load environment variables
load_dotenv(find_dotenv())

# Check if API key is loaded
gemini_api_key = os.getenv("GEMINI_API_KEY")

print("=== API Key Debug Info ===")
print(f"API Key loaded: {gemini_api_key is not None}")
print(f"API Key length: {len(gemini_api_key) if gemini_api_key else 0}")
print(f"API Key starts with: {gemini_api_key[:10] if gemini_api_key else 'None'}...")
print(f"API Key ends with: ...{gemini_api_key[-10:] if gemini_api_key else 'None'}")

# Check .env file location
env_file = find_dotenv()
print(f"Found .env file at: {env_file}")

# Test a simple API call
try:
    import requests
    
    headers = {
        "Content-Type": "application/json",
    }
    
    # Simple test to Google AI API
    test_url = f"https://generativelanguage.googleapis.com/v1beta/models?key={gemini_api_key}"
    
    print("\n=== Testing API Connection ===")
    response = requests.get(test_url)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ API Key is valid!")
    else:
        print("❌ API Key issue:")
        print(response.text)
        
except Exception as e:
    print(f"Error testing API: {e}")
