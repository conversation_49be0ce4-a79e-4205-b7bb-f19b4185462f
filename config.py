import os
from dotenv import load_dotenv, find_dotenv
from openai import Async<PERSON>penAI

# Load environment variables
load_dotenv(find_dotenv())
gemini_api_key = os.getenv("GEMINI_API_KEY")

# Setup client using direct OpenAI client (agents library has issues)
client = AsyncOpenAI(
    api_key=gemini_api_key,
    base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
)

# Model name for easy reference
model_name = "gemini-2.0-flash"
