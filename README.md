# 🤖 AI Chatbot with Chainlit & Google Gemini

A modern web-based AI chatbot built with Chainlit and powered by Google's Gemini 2.0 Flash model. This project demonstrates how to create an interactive AI assistant with real-time streaming responses and conversation memory.

## 🌟 Features

- **Real-time AI Responses**: Stream responses word-by-word like ChatGPT
- **Conversation Memory**: AI remembers previous messages in the conversation
- **Modern Web Interface**: Clean, user-friendly chat interface powered by Chainlit
- **Google Gemini Integration**: Uses Google's advanced Gemini 2.0 Flash model
- **Customizable Agent**: Easy to modify AI personality and instructions

## 🔧 Technologies Used

- **[Chainlit](https://chainlit.io/)** - Web interface framework for AI applications
- **[OpenAI Agents](https://github.com/openai/openai-agents)** - Agent framework for AI interactions
- **[Google Gemini API](https://ai.google.dev/)** - Advanced AI model for generating responses
- **[Python](https://python.org/)** - Programming language
- **[UV](https://docs.astral.sh/uv/)** - Modern Python package manager

## 📋 Prerequisites

- Python 3.11 or higher
- UV package manager
- Google Gemini API key

## 🚀 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/asadullah48/[your-repo-name].git
   cd [your-repo-name]
   ```

2. **Install dependencies using UV**
   ```bash
   uv sync
   ```

3. **Set up environment variables**
   Create a `.env` file in the project root:
   ```env
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

4. **Get your Gemini API key**
   - Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
   - Create a new API key
   - Add it to your `.env` file

## 🎯 Usage

1. **Start the application**
   ```bash
   uv run chainlit run main.py
   ```

2. **Open your browser**
   - Navigate to `http://localhost:8000`
   - Start chatting with your AI agent!

## 📁 Project Structure

```
├── main.py           # Main application file
├── chainlit.md       # Welcome page content
├── pyproject.toml    # Project dependencies and metadata
├── uv.lock          # Dependency lock file
├── .env             # Environment variables (create this)
└── README.md        # This file
```

## 🔍 How It Works

### 1. **Setup Phase**
The application initializes by:
- Loading environment variables (API keys)
- Creating a connection to Google's Gemini API
- Setting up an AI agent with custom instructions

### 2. **Chat Interface**
When users visit the web interface:
- Chainlit creates a clean chat interface
- A welcome message greets the user
- Conversation history is initialized

### 3. **Message Processing**
When a user sends a message:
- The message is added to conversation history
- The entire history is sent to the Gemini model
- AI response is streamed back in real-time
- History is updated with the AI's response

## 🛠️ Customization

### Change AI Personality
Modify the agent instructions in `main.py`:

```python
agent = Agent(
    name="Your Agent Name",
    instructions="Your custom instructions here",
    model=model
)
```

### Modify Welcome Message
Edit the greeting in the `handle_start_chat` function:

```python
await cl.Message(content="Your custom welcome message").send()
```

### Update Welcome Page
Edit `chainlit.md` to customize the landing page content.

## 🔑 Key Code Components

### Agent Configuration
```python
agent = Agent(
    name="Agent Learner",
    instructions="Greeting from Agent Learner",
    model=model
)
```

### Real-time Streaming
```python
async for event in result.stream_events():
    if event.type == "raw_response_event" and isinstance(event.data, ResponseTextDeltaEvent):
        await msg.stream_token(event.data.delta)
```

### Conversation Memory
```python
history = cl.user_session.get("history")
history.append({"role": "user", "content": message.content})
```

## 🐛 Troubleshooting

### Common Issues

1. **API Key Error**
   - Ensure your `.env` file exists and contains a valid `GEMINI_API_KEY`
   - Check that the API key has proper permissions

2. **Port Already in Use**
   - Change the port: `chainlit run main.py --port 8001`

3. **Dependencies Issues**
   - Run `uv sync` to ensure all dependencies are installed
   - Check Python version compatibility (3.11+)

## 📚 Learning Resources

- [Chainlit Documentation](https://docs.chainlit.io/)
- [Google Gemini API Docs](https://ai.google.dev/docs)
- [OpenAI Agents Documentation](https://github.com/openai/openai-agents)
- [UV Package Manager](https://docs.astral.sh/uv/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 👨‍💻 Author

**Asadullah**
- GitHub: [@asadullah48](https://github.com/asadullah48)

## 🙏 Acknowledgments

- [Chainlit](https://chainlit.io/) for the amazing web framework
- [Google](https://ai.google.dev/) for the Gemini API
- [Astral](https://astral.sh/) for the UV package manager

---

⭐ If you found this project helpful, please give it a star on GitHub!