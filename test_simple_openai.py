import os
import asyncio
from dotenv import load_dotenv
from openai import AsyncOpenAI

# Load environment variables
load_dotenv()
gemini_api_key = os.getenv("GEMINI_API_KEY")

print(f"Testing with API key: {gemini_api_key[:10]}...{gemini_api_key[-5:]}")

# Create OpenAI client for Gemini
client = AsyncOpenAI(
    api_key=gemini_api_key,
    base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
)

async def test_simple():
    try:
        print("Testing simple completion...")
        
        response = await client.chat.completions.create(
            model="gemini-2.0-flash",
            messages=[{"role": "user", "content": "Say hello"}],
            stream=False
        )
        
        print(f"✅ Success: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def test_streaming():
    try:
        print("Testing streaming completion...")
        
        response = await client.chat.completions.create(
            model="gemini-2.0-flash",
            messages=[{"role": "user", "content": "Count from 1 to 5"}],
            stream=True
        )
        
        print("Streaming response:")
        full_response = ""
        async for chunk in response:
            if chunk.choices[0].delta.content:
                content = chunk.choices[0].delta.content
                full_response += content
                print(content, end="", flush=True)
        
        print(f"\n✅ Streaming success: {full_response}")
        return True
        
    except Exception as e:
        print(f"❌ Streaming error: {e}")
        return False

async def main():
    print("=== Testing Direct OpenAI Client ===")
    
    simple_works = await test_simple()
    if simple_works:
        streaming_works = await test_streaming()
        
        if streaming_works:
            print("\n🎉 Both simple and streaming work! The issue is with the agents library.")
        else:
            print("\n⚠️ Simple works but streaming doesn't.")
    else:
        print("\n❌ Basic API call failed.")

if __name__ == "__main__":
    asyncio.run(main())
