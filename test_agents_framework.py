import os
import asyncio
from dotenv import load_dotenv, find_dotenv
from agents import AsyncOpenAI, OpenAIChatCompletionsModel, Agent, Runner
from agents.run import RunConfig

# Load environment variables
load_dotenv(find_dotenv())
gemini_api_key = os.getenv("GEMINI_API_KEY")

print("=== Testing Agents Framework ===")
print(f"API Key: {gemini_api_key[:10]}...{gemini_api_key[-5:]}")

# Setup exactly like your config.py
external_client = AsyncOpenAI(
    api_key=gemini_api_key,
    base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
)

model = OpenAIChatCompletionsModel(
    model="gemini-2.0-flash",
    openai_client=external_client
)

config = RunConfig(
    model=model,
    model_provider=external_client,
    tracing_disabled=True
)

agent = Agent(
    name="Test Agent",
    instructions="You are a helpful assistant",
    model=model
)

async def test_agents():
    print("\n=== Testing Non-Streamed Run ===")
    try:
        # Test non-streamed first
        result = await Runner.run(
            agent,
            input=[{"role": "user", "content": "Say hello"}],
            run_config=config
        )
        print(f"✅ Non-streamed success: {result.final_output}")

    except Exception as e:
        print(f"❌ Non-streamed failed: {e}")
        return False

    print("\n=== Testing Streamed Run ===")
    try:
        # Test streamed (like your app)
        result = await Runner.run_streamed(
            agent,
            input=[{"role": "user", "content": "Say hello"}],
            run_config=config
        )

        print("Streaming events...")
        async for event in result.stream_events():
            print(f"Event: {event.type}")
            if hasattr(event, 'data'):
                print(f"Data type: {type(event.data)}")

        print(f"✅ Streamed success: {result.final_output}")
        return True

    except Exception as e:
        print(f"❌ Streamed failed: {e}")
        return False

# Run the test
if __name__ == "__main__":
    asyncio.run(test_agents())
