import os
from dotenv import load_dotenv, find_dotenv

print("=== Environment Debug ===")

# Test different ways of loading .env
print("1. Using find_dotenv():")
env_file = find_dotenv()
print(f"   Found .env at: {env_file}")
load_dotenv(env_file)
key1 = os.getenv("GEMINI_API_KEY")
print(f"   API Key: {key1[:10] if key1 else 'None'}...{key1[-5:] if key1 else 'None'}")

print("\n2. Using load_dotenv() without find_dotenv:")
load_dotenv()
key2 = os.getenv("GEMINI_API_KEY")
print(f"   API Key: {key2[:10] if key2 else 'None'}...{key2[-5:] if key2 else 'None'}")

print("\n3. Using explicit .env path:")
load_dotenv(".env")
key3 = os.getenv("GEMINI_API_KEY")
print(f"   API Key: {key3[:10] if key3 else 'None'}...{key3[-5:] if key3 else 'None'}")

print("\n4. Reading .env file directly:")
try:
    with open(".env", "r") as f:
        content = f.read()
        print(f"   .env content: {content}")
        # Parse manually
        for line in content.strip().split('\n'):
            if line.startswith('GEMINI_API_KEY='):
                manual_key = line.split('=', 1)[1].strip()
                print(f"   Manual parse: {manual_key[:10]}...{manual_key[-5:]}")
except Exception as e:
    print(f"   Error reading .env: {e}")

print("\n5. All environment variables containing 'GEMINI':")
for key, value in os.environ.items():
    if 'GEMINI' in key.upper():
        print(f"   {key}: {value[:10] if value else 'None'}...{value[-5:] if value else 'None'}")

print("\n6. Current working directory:")
print(f"   {os.getcwd()}")

print("\n7. Files in current directory:")
for file in os.listdir('.'):
    if file.startswith('.env'):
        print(f"   {file}")
