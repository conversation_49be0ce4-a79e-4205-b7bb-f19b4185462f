import os
import chainlit as cl
from dotenv import load_dotenv
from openai import AsyncOpenAI

# Load environment variables
load_dotenv()
gemini_api_key = os.getenv("GEMINI_API_KEY")

# Create OpenAI client for Gemini
client = AsyncOpenAI(
    api_key=gemini_api_key,
    base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
)

@cl.on_chat_start
async def handle_start_chat():
    cl.user_session.set("history", [])
    await cl.Message(content="Assalaam O Alikum! I'm your AI assistant powered by Gemini. How can I help you?").send()

@cl.on_message
async def handle_message(message: cl.Message):
    # Get conversation history
    history = cl.user_session.get("history", [])
    
    # Add user message to history
    history.append({"role": "user", "content": message.content})
    
    try:
        print(f"Sending to Gemini: {history}")
        
        # Call Gemini API directly
        response = await client.chat.completions.create(
            model="gemini-2.0-flash",
            messages=history,
            stream=True  # Enable streaming for real-time response
        )
        
        # Create message for streaming response
        msg = cl.Message(content="")
        await msg.send()
        
        # Stream the response
        full_response = ""
        async for chunk in response:
            if chunk.choices[0].delta.content:
                content = chunk.choices[0].delta.content
                full_response += content
                await msg.stream_token(content)
        
        # Add assistant response to history
        history.append({"role": "assistant", "content": full_response})
        cl.user_session.set("history", history)
        
        print(f"Response sent: {full_response}")
        
    except Exception as e:
        error_msg = f"Error: {str(e)}"
        print(f"Error occurred: {error_msg}")
        await cl.Message(content=f"Sorry, I encountered an error: {error_msg}").send()
