from agents import Runner, Agent
import chainlit as cl
import asyncio

# Import from our new config
try:
    from config_v2 import config, model
    print("Successfully imported from config_v2")
except Exception as e:
    print(f"Error importing config_v2: {e}")
    exit(1)

# Create agent
agent = Agent(
    name="Simple Agent",
    instructions="You are a helpful assistant. Keep responses short and friendly.",
    model=model
)

@cl.on_chat_start
async def handle_start_chat():
    await cl.Message(content="Hello! I'm your AI assistant. How can I help you today?").send()

@cl.on_message
async def handle_message(message: cl.Message):
    print(f"Received message: {message.content}")
    
    # Create a simple message for the AI
    user_input = [{"role": "user", "content": message.content}]
    
    try:
        print("Calling Runner.run...")
        
        # Use the simplest possible approach
        result = await Runner.run(
            agent,
            input=user_input,
            run_config=config
        )
        
        print(f"Got result: {result.final_output}")
        
        # Send the response
        await cl.Message(content=result.final_output).send()
        
    except Exception as e:
        error_msg = f"Error: {str(e)}"
        print(error_msg)
        await cl.Message(content=f"Sorry, I encountered an error: {error_msg}").send()
