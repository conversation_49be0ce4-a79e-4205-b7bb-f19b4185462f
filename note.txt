===========================================
📚 CONFIG.PY LEARNING NOTES
===========================================
Author: Asadullah (@asadullah48)
Purpose: Centralized configuration for AI projects
Date: Learning Journey Notes

===========================================
🎯 WHAT IS CONFIG.PY?
===========================================

A configuration file that centralizes all AI model setup and configuration.
Instead of repeating the same setup code in every project, you create it once
and reuse it across all your learning projects.

BENEFITS:
✅ Centralized Configuration - All AI setup in one place
✅ Reusability - Copy config.py to any new project  
✅ Maintainability - Change model settings in one file
✅ Clean Code - Main files focus on logic, not setup
✅ Learning Efficiency - Spend time on new concepts, not setup

===========================================
🔧 YOUR CURRENT CONFIG.PY STRUCTURE
===========================================

import os
from dotenv import load_dotenv, find_dotenv
from agents import AsyncOpenAI, OpenAIChatCompletionsModel
from agents.run import RunConfig

# Load environment variables
load_dotenv(find_dotenv())
gemini_api_key = os.getenv("GEMINI_API_KEY")

# Setup client 
external_client = AsyncOpenAI(
    api_key=gemini_api_key,
    base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
)

# Preferred model setup
model = OpenAIChatCompletionsModel(
    model="gemini-2.0-flash",
    openai_client=external_client
)

# Runner config
config = RunConfig(
    model=model,
    model_provider=external_client,
    tracing_disabled=True
)

===========================================
🚀 HOW TO USE IN NEW PROJECTS
===========================================

STEP 1: Copy config.py to your new project folder

STEP 2: Import what you need in your main.py:
   from config import config, model, external_client

STEP 3: Use immediately without setup code:
   agent = Agent(name="My Agent", model=model)
   result = Runner.run(agent, input="Hello", run_config=config)

BEFORE CONFIG.PY (repetitive):
   - 20+ lines of setup code in every project
   - Easy to make mistakes
   - Hard to maintain

AFTER CONFIG.PY (clean):
   - 1 line import
   - Consistent across projects
   - Easy to update

===========================================
🔄 REFACTORING EXISTING PROJECTS
===========================================

OLD WAY (in main.py):
from agents import Runner, Agent, AsyncOpenAI, OpenAIChatCompletionsModel, RunConfig
from dotenv import load_dotenv
import os

load_dotenv()
gemini_api_key = os.getenv("GEMINI_API_KEY")
external_client = AsyncOpenAI(api_key=gemini_api_key, base_url="...")
model = OpenAIChatCompletionsModel(model="gemini-2.0-flash", openai_client=external_client)
config = RunConfig(model=model, model_provider=external_client, tracing_disabled=True)

NEW WAY (with config.py):
from agents import Runner, Agent
from config import config, model

===========================================
🌟 PROJECT TYPES YOU CAN BUILD
===========================================

1. CHATBOTS (like your current project)
   - Web interfaces with Chainlit
   - Discord bots
   - Telegram bots

2. CLI TOOLS
   - Command-line AI assistants
   - Text processors
   - Code generators

3. API SERVICES
   - FastAPI endpoints
   - Flask applications
   - REST APIs

4. DATA ANALYSIS TOOLS
   - CSV analyzers
   - Report generators
   - Data visualizers

5. CONTENT GENERATORS
   - Blog post writers
   - Code documenters
   - Email composers

===========================================
📝 EXAMPLE USAGE PATTERNS
===========================================

CHATBOT EXAMPLE:
from config import model, config
from agents import Agent

agent = Agent(
    name="Learning Bot",
    instructions="Help students learn programming",
    model=model
)

CLI TOOL EXAMPLE:
from config import model, config
from agents import Agent, Runner

agent = Agent(name="CLI Helper", model=model)
result = Runner.run(agent, input="Explain Python loops", run_config=config)
print(result.final_output)

API SERVICE EXAMPLE:
from fastapi import FastAPI
from config import model, config
from agents import Agent, Runner

app = FastAPI()
agent = Agent(name="API Agent", model=model)

@app.post("/chat")
async def chat(message: str):
    result = Runner.run(agent, input=message, run_config=config)
    return {"response": result.final_output}

===========================================
🔧 ADVANCED CONFIG PATTERNS
===========================================

CLASS-BASED CONFIG (for complex projects):

class AIConfig:
    def __init__(self):
        load_dotenv(find_dotenv())
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        self.model_name = os.getenv("MODEL_NAME", "gemini-2.0-flash")
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/openai/"
    
    def get_client(self):
        return AsyncOpenAI(api_key=self.gemini_api_key, base_url=self.base_url)
    
    def get_model(self):
        return OpenAIChatCompletionsModel(
            model=self.model_name,
            openai_client=self.get_client()
        )

MULTIPLE MODELS CONFIG:

# For projects that need different AI models
gemini_model = OpenAIChatCompletionsModel(model="gemini-2.0-flash", ...)
gpt_model = OpenAIChatCompletionsModel(model="gpt-4", ...)

ENVIRONMENT-SPECIFIC CONFIG:

# Different settings for development vs production
if os.getenv("ENVIRONMENT") == "production":
    tracing_disabled = True
else:
    tracing_disabled = False

===========================================
🌍 ENVIRONMENT VARIABLES STRATEGY
===========================================

YOUR .ENV FILE SHOULD INCLUDE:

# AI Configuration
GEMINI_API_KEY=your_gemini_key_here
MODEL_NAME=gemini-2.0-flash
OPENAI_API_KEY=your_openai_key_for_future_projects

# Environment Settings
ENVIRONMENT=development
DEBUG=true

# Database (for future projects)
DATABASE_URL=your_database_url

# Other APIs (for future integrations)
WEATHER_API_KEY=your_weather_key
NEWS_API_KEY=your_news_key

SECURITY NOTES:
- Never commit .env files to GitHub
- Add .env to your .gitignore file
- Use different .env files for different environments

===========================================
🐛 TROUBLESHOOTING COMMON ISSUES
===========================================

ISSUE: ImportError when importing from config
SOLUTION: Make sure config.py is in the same directory as your main.py

ISSUE: API key not found
SOLUTION: Check that .env file exists and GEMINI_API_KEY is set

ISSUE: Model not working
SOLUTION: Verify your API key has proper permissions

ISSUE: Config changes not taking effect
SOLUTION: Restart your application after changing config.py

===========================================
📚 LEARNING PROGRESSION
===========================================

BEGINNER LEVEL:
1. Use basic config.py as-is
2. Copy to new projects
3. Modify agent instructions

INTERMEDIATE LEVEL:
1. Add multiple model support
2. Create environment-specific configs
3. Add error handling

ADVANCED LEVEL:
1. Create config classes
2. Add configuration validation
3. Support multiple AI providers

===========================================
🎯 NEXT LEARNING STEPS
===========================================

1. CREATE A SIMPLE CLI TOOL
   - Use your config.py
   - Practice importing and using model
   - Experiment with different agent instructions

2. BUILD DIFFERENT PROJECT TYPES
   - Try a FastAPI service
   - Create a data analysis tool
   - Build a content generator

3. ENHANCE YOUR CONFIG
   - Add error handling
   - Support multiple models
   - Create configuration validation

4. LEARN BEST PRACTICES
   - Study software architecture patterns
   - Learn about dependency injection
   - Understand configuration management

===========================================
💡 KEY TAKEAWAYS FOR YOUR JOURNEY
===========================================

1. CONSISTENCY IS KEY
   - Use the same config pattern across all projects
   - This builds good habits and muscle memory

2. START SIMPLE, GROW COMPLEX
   - Begin with basic config.py
   - Add features as you learn more

3. FOCUS ON LEARNING, NOT SETUP
   - Config.py eliminates repetitive setup
   - Spend time on new concepts instead

4. BUILD A PERSONAL TOOLKIT
   - Your config.py is part of your developer toolkit
   - Keep improving it as you learn

5. DOCUMENT YOUR LEARNING
   - Keep notes like this one
   - Track what works and what doesn't

===========================================
🔗 USEFUL RESOURCES
===========================================

- Chainlit Documentation: https://docs.chainlit.io/
- OpenAI Agents: https://github.com/openai/openai-agents
- Google Gemini API: https://ai.google.dev/docs
- Python dotenv: https://pypi.org/project/python-dotenv/
- UV Package Manager: https://docs.astral.sh/uv/

===========================================
📝 PERSONAL LEARNING LOG
===========================================

Date: [Add dates as you learn]
Project: [Project name]
What I learned: [Key learnings]
Challenges: [What was difficult]
Next steps: [What to try next]

EXAMPLE ENTRY:
Date: 2025-07-06
Project: Chainlit Chatbot
What I learned: How to centralize AI configuration
Challenges: Understanding import statements
Next steps: Try building a CLI tool

===========================================
END OF NOTES
===========================================

Remember: The goal is to learn efficiently and build consistently.
Your config.py approach will save you time and help you focus on
what matters most - learning new AI concepts and building cool projects!

Keep coding, keep learning! 🚀
